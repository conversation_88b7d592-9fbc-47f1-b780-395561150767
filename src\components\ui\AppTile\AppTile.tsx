import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface AppTileProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
  color?: string; // Primary color for the app
  gradient?: boolean; // Whether to use gradient background for icon
  isActive?: boolean; // Whether the app is active/available
  isPremium?: boolean; // Whether this is a premium app
}

const AppTile: React.FC<AppTileProps> = ({
  title,
  description,
  icon,
  onClick,
  disabled = false,
  className = '',
  'data-testid': testId,
  color = '#f97316', // Default orange color
  gradient = true,
  isActive = true,
  isPremium = false,
}) => {
  const { colors, isDark } = useThemeStore();
  const [showModal, setShowModal] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const tileRef = useRef<HTMLDivElement>(null);
  const infoButtonRef = useRef<HTMLButtonElement>(null);

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const isInactive = !isActive;
  
  const baseClasses = cn(
    'relative group cursor-pointer',
    'transition-all duration-300 ease-out',
    'focus:outline-none',
    'flex flex-col items-center justify-center text-center',
    'px-2 py-4 min-h-[100px]',
    disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
    isInactive && 'opacity-60',
    className
  );

  // Card styles for icon container
  const iconCardStyles = {
    backgroundColor: 'transparent',
    transition: 'all 0.3s ease-out',
  };

  const iconCardHoverStyles = {
    transform: 'translateY(-2px) scale(1.02)',
    boxShadow: isDark
      ? `0 12px 30px -8px ${color}30, 0 8px 20px -5px rgba(0, 0, 0, 0.4)`
      : `0 12px 30px -8px ${color}20, 0 8px 20px -5px rgba(0, 0, 0, 0.1)`,
    backgroundColor: isDark
      ? 'rgba(30, 41, 59, 0.95)'
      : 'rgba(248, 250, 252, 1)',
    borderRadius: '16px',
  };

  // Create gradient background for icon
  const getIconBackground = () => {
    if (!gradient) return color;

    // Create gradient based on the primary color
    const baseColor = color;
    const lighterColor = adjustColorBrightness(baseColor, 20);
    const darkerColor = adjustColorBrightness(baseColor, -20);

    return `linear-gradient(135deg, ${lighterColor} 0%, ${baseColor} 50%, ${darkerColor} 100%)`;
  };

  // Helper function to adjust color brightness
  const adjustColorBrightness = (hex: string, percent: number) => {
    const num = parseInt(hex.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = ((num >> 8) & 0x00ff) + amt;
    const B = (num & 0x0000ff) + amt;
    return (
      '#' +
      (
        0x1000000 +
        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
        (B < 255 ? (B < 1 ? 0 : B) : 255)
      )
        .toString(16)
        .slice(1)
    );
  };

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle body scroll lock when modal is open
  useEffect(() => {
    if (showModal) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [showModal]);

  const handleInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent tile click
    setShowModal(true);
  };

  const handleTileClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleCloseModal();
    }
  };

  return (
    <>
      <div
        ref={tileRef}
        className={baseClasses}
        onClick={handleTileClick}
        data-testid={testId}
        tabIndex={disabled ? -1 : 0}
        role="button"
        aria-disabled={disabled}
      >
        {/* Premium Badge */}
        {isPremium && (
          <div className="absolute top-2 right-2 z-20">
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg animate-pulse">
              🚫 Inactive
            </div>
          </div>
        )}

        <div className="relative z-10 flex flex-col items-center text-center space-y-3 sm:space-y-4">
          {/* Icon with card effect */}
          {icon && (
            <div className="relative">
              <div
                className={cn(
                  "relative flex items-center justify-center transition-all duration-300",
                  "text-4xl sm:text-5xl p-4 rounded-2xl",
                  !isInactive && "group-hover:scale-105",
                  isInactive && "grayscale opacity-50"
                )}
                style={iconCardStyles}
                onMouseEnter={e => {
                  if (!disabled && !isInactive) {
                    Object.assign(e.currentTarget.style, iconCardHoverStyles);
                  }
                }}
                onMouseLeave={e => {
                  if (!disabled && !isInactive) {
                    Object.assign(e.currentTarget.style, iconCardStyles);
                  }
                }}
              >
                <div style={{ color: color }}>
                  {icon}
                </div>
                
                {/* Info button - only show if there's a description */}
                {description && (
                  <button
                    ref={infoButtonRef}
                    onClick={handleInfoClick}
                    className={cn(
                      "absolute -top-1 -right-1 w-5 h-5 rounded-full",
                      "flex items-center justify-center text-xs",
                      "transition-all duration-200 hover:scale-110",
                      "focus:outline-none focus:ring-2 focus:ring-offset-1",
                      isDark ? "bg-slate-700 text-slate-300 hover:bg-slate-600 focus:ring-slate-500"
                             : "bg-gray-200 text-gray-600 hover:bg-gray-300 focus:ring-gray-400"
                    )}
                    aria-label={`Show description for ${title}`}
                    type="button"
                  >
                    <svg
                      width="10"
                      height="10"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="opacity-70"
                    >
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                    </svg>
                  </button>
                )}

                {/* Small highlight dot - only show for active apps */}
                {isActive && !description && (
                  <div
                    className="absolute -top-1 -right-1 w-3 h-3 rounded-full opacity-80"
                    style={{
                      background:
                        'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4))',
                    }}
                  />
                )}
              </div>
            </div>
          )}
        </div>

        {/* Title outside the card */}
        <div className="mt-2 space-y-1">
          <h3
            className={cn(
              "font-semibold leading-tight text-sm sm:text-base",
              isInactive && "opacity-70"
            )}
            style={{ color: colors.text }}
          >
            {title}
          </h3>
        </div>
      </div>

      {/* Modal Sidebar (Desktop) / Bottom Sheet (Mobile) */}
      {description && showModal && (
        <div
          className="fixed inset-0 z-50 flex items-end justify-end"
          onClick={handleBackdropClick}
        >
          {/* Backdrop */}
          <div
            className={cn(
              'absolute inset-0 transition-opacity duration-300',
              showModal ? 'opacity-100' : 'opacity-0'
            )}
            style={{
              background: 'rgba(0, 0, 0, 0.5)',
              backdropFilter: 'blur(4px)',
            }}
          />

          {/* Modal Content */}
          <div
            className={cn(
              'relative transition-all duration-300 ease-out',
              // Desktop: Sidebar from right
              !isMobile && [
                'h-full w-96 max-w-[90vw]',
                'transform',
                showModal ? 'translate-x-0' : 'translate-x-full'
              ],
              // Mobile: Bottom sheet
              isMobile && [
                'w-full max-h-[80vh]',
                'rounded-t-2xl',
                'transform',
                showModal ? 'translate-y-0' : 'translate-y-full'
              ]
            )}
            style={{
              background: isDark
                ? 'rgba(15, 23, 42, 0.98)'
                : 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              boxShadow: isDark
                ? '0 -10px 40px rgba(0, 0, 0, 0.3)'
                : '0 -10px 40px rgba(0, 0, 0, 0.1)',
            }}
          >
            {/* Mobile drag handle */}
            {isMobile && (
              <div className="flex justify-center pt-3 pb-2">
                <div
                  className="w-12 h-1 rounded-full opacity-30"
                  style={{ backgroundColor: colors.text }}
                />
              </div>
            )}

            {/* Header */}
            <div
              className={cn(
                'flex items-center justify-between p-6',
                isMobile ? 'pb-4' : 'border-b',
                !isMobile && (isDark ? 'border-slate-700' : 'border-gray-200')
              )}
            >
              <div className="flex items-center space-x-3">
                <div
                  className="flex items-center justify-center w-10 h-10 rounded-xl"
                  style={{
                    backgroundColor: `${color}20`,
                    color: color,
                  }}
                >
                  {icon}
                </div>
                <div>
                  <h3
                    className="text-lg font-semibold"
                    style={{ color: colors.text }}
                  >
                    {title}
                  </h3>
                  <p
                    className="text-sm opacity-70"
                    style={{ color: colors.textSecondary }}
                  >
                    App Details
                  </p>
                </div>
              </div>
              
              <button
                onClick={handleCloseModal}
                className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center',
                  'transition-colors duration-200',
                  'hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2',
                  isDark
                    ? 'hover:bg-white focus:ring-slate-500'
                    : 'hover:bg-black focus:ring-gray-400'
                )}
                aria-label="Close details"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  style={{ color: colors.textSecondary }}
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-4">
              <div>
                <h4
                  className="text-sm font-medium mb-2 opacity-70"
                  style={{ color: colors.textSecondary }}
                >
                  Description
                </h4>
                <p
                  className="text-base leading-relaxed"
                  style={{ color: colors.text }}
                >
                  {description}
                </p>
              </div>

              {/* Additional details section */}
              <div className="pt-4 space-y-3">
                <div className="flex justify-between items-center">
                  <span
                    className="text-sm opacity-70"
                    style={{ color: colors.textSecondary }}
                  >
                    Status
                  </span>
                  <span
                    className={cn(
                      'text-sm font-medium px-2 py-1 rounded-full',
                      isActive
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    )}
                  >
                    {isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                
                {isPremium && (
                  <div className="flex justify-between items-center">
                    <span
                      className="text-sm opacity-70"
                      style={{ color: colors.textSecondary }}
                    >
                      Type
                    </span>
                    <span className="text-sm font-medium px-2 py-1 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white">
                      Premium
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Footer Actions */}
            <div
              className={cn(
                'p-6 pt-4',
                !isMobile && 'border-t',
                !isMobile && (isDark ? 'border-slate-700' : 'border-gray-200')
              )}
            >
              <button
                onClick={() => {
                  handleCloseModal();
                  handleTileClick();
                }}
                disabled={disabled || !isActive}
                className={cn(
                  'w-full py-3 px-4 rounded-xl font-medium transition-all duration-200',
                  'focus:outline-none focus:ring-2 focus:ring-offset-2',
                  disabled || !isActive
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-lg transform hover:scale-[1.02]'
                )}
                style={{
                  backgroundColor: disabled || !isActive ? colors.textSecondary + '20' : color,
                  color: disabled || !isActive ? colors.textSecondary : 'white',
                }}
              >
                {disabled ? 'Unavailable' : !isActive ? 'Inactive' : `Open ${title}`}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AppTile;
